import { Component } from '@angular/core';
import { CoreData } from '../../Services/core-data';

@Component({
  selector: 'app-trend',
  standalone: false,
  templateUrl: '../Template/trend.html',
  styleUrl: '../style/trend.css'
})
export class Trend {

  topics: string[] = [];
  generatedPost: any;

  constructor(private trendingService: CoreData) {}

  ngOnInit() {
    this.trendingService.getTopics().subscribe(res => this.topics = res);
  }

  generate(topic: string) {
    this.trendingService.generatePost(topic).subscribe(res => this.generatedPost = res);
  }

  publish() {
    this.trendingService.publishPost(this.generatedPost.caption, this.generatedPost.imageUrl)
      .subscribe(res => alert('Posted on Instagram!'));
  }
}
