import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CoreData {

  private baseUrl = 'https://localhost:5001/api/trending';

  constructor(private http: HttpClient) {}

  getTopics(): Observable<string[]> {
    return this.http.get<string[]>(`${this.baseUrl}/topics`);
  }

  generatePost(topic: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/generate`, { topic });
  }

  publishPost(caption: string, imageUrl: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/publish`, { caption, imageUrl });
  }
}
